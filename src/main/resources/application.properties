server.port=3131
spring.application.name=nacos-consumer

spring.cloud.nacos.discovery.server-addr=192.168.100.128:8848

# 连接超时时间
nacos-provider.ribbon.ConnectTimeout=1000
# 服务器处理请求超时时间
nacos-provider.ribbon.ReadTimeout=5000
# 对所有操作进行重试工作
nacos-provider.ribbon.OkToRetryOnAllOperations=true
# 超时的时候,最大重试次数
nacos-provider.ribbon.MaxAutoRetries=2
# 如果在调用当前服务重试次数没了,就换个服务
nacos-provider.ribbon.MaxAutoRetriesNextServer=1
# 负载均衡策略
# nacos-provider.ribbon.NFLoadBalancerRuleClassName=com.netflix.loadbalancer.RoundRobinRule
nacos-provider.ribbon.NFLoadBalancerRuleClassName=com.alibaba.cloud.nacos.ribbon.NacosRule