package com.demo.nacosspringcloudconsumer.config;

import com.alibaba.cloud.nacos.ribbon.NacosRule;
import com.netflix.loadbalancer.IRule;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * @Name ConsumerConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/6 11:16
 */
@Configuration
public class ConsumerConfig {

    @Bean
    @LoadBalanced
    RestTemplate restTemplate() {
        return new RestTemplate();
    }

//    @Bean
//    public IRule ribbonRule() {
//        return new NacosRule();
//    }
}
